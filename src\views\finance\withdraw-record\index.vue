<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 数据汇总区域 -->
      <el-row :gutter="20" class="summary-container">
        <el-col :span="5">
          <div class="summary-item">
            <div class="label">今日提现总额</div>
            <div class="value">{{ formatNumber(summary.todayAmount || 0) }}USDT</div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="summary-item">
            <div class="label">今日提现笔数</div>
            <div class="value">{{ summary.todayCount || 0 }}笔</div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="summary-item">
            <div class="label">待审核总额</div>
            <div class="value">{{ formatNumber(summary.pendingAmount || 0) }}USDT</div>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="summary-item">
            <div class="label">待审核笔数</div>
            <div class="value">{{ summary.pendingCount || 0 }}笔</div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="summary-item">
            <div class="label">已通过总额</div>
            <div class="value">{{ formatNumber(summary.totalPassAmount || 0) }}USDT</div>
          </div>
        </el-col>
      </el-row>

      <!-- 搜索区域 -->
      <div class="filter-container">
        <el-input
          v-model.trim="listQuery.username"
          placeholder="用户名/邮箱"
          style="width: 200px;"
          class="filter-item"
          clearable
          @clear="handleSearch"
          @keyup.enter.native="handleSearch"
        />
        <el-select
          v-model="listQuery.status"
          placeholder="提现状态"
          clearable
          class="filter-item"
          style="width: 130px"
          @change="handleSearch"
        >
          <el-option label="待审核" value="0" />
          <el-option label="已通过" value="1" />
          <el-option label="已拒绝" value="2" />
          <el-option label="未通过" value="3" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          class="filter-item"
          @change="handleDateRangeChange"
        />
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
        <el-button type="success" icon="el-icon-refresh" @click="handleReset">重置</el-button>
        <el-button 
          type="warning" 
          icon="el-icon-download" 
          @click="handleExport"
          :loading="exportLoading"
        >导出</el-button>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <!-- <el-table-column label="用户ID" prop="userId" min-width="80" align="center" /> -->
        <el-table-column label="用户名称" prop="username" min-width="120" align="center" />
        <el-table-column label="注册邮箱" prop="registerEmail" min-width="150" align="center" />
        <el-table-column label="提现金额" align="center" min-width="120">
          <template slot-scope="scope">
            <span style="color: #f56c6c">{{ formatNumber(scope.row.amount) }}USDT</span>
          </template>
        </el-table-column>
        <el-table-column label="手续费" align="center" min-width="100">
          <template slot-scope="scope">
            <span style="color: #909399">{{ formatNumber(scope.row.fee) }}USDT</span>
          </template>
        </el-table-column>
        <el-table-column label="实际到账" align="center" min-width="120">
          <template slot-scope="scope">
            <span style="color: #67C23A">{{ formatNumber(scope.row.realAmount) }}USDT</span>
          </template>
        </el-table-column>
        <el-table-column label="提现地址" prop="address" min-width="200" align="center">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.address" placement="top">
              <span>{{ scope.row.address ? scope.row.address.substring(0, 20) + '...' : '-' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="链名称" prop="chainName" min-width="100" align="center" />
        <el-table-column label="状态" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="申请时间" min-width="160" align="center">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <!-- :disabled="isWithinCooldown(scope.row.createTime, scope.row.status)" -->
        <el-table-column label="操作" min-width="120" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" @click="handleDetail(scope.row)">详情</el-button>
            <!-- 自动处理按钮 -->
            <el-button
              v-if="scope.row.status === 0 && shouldShowAutoProcess(scope.row)"
              type="text"
              class="auto-process-btn"
            >
            </el-button>
            <span></span>
            <!-- 审核按钮 -->
            <el-button
              v-if="scope.row.status === 0 && !shouldShowAutoProcess(scope.row)"
              type="text"
              @click="handleAudit(scope.row)"
            >
              {{ getAuditButtonText(scope.row.createTime, scope.row.status) }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="listQuery.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="提现详情" :visible.sync="detailVisible" width="700px">
      <el-card class="detail-card">
        <div slot="header">
          <span class="card-title">用户信息</span>
        </div>
        <el-descriptions :column="2" border>
          <!-- <el-descriptions-item label="用户ID" width="180px">{{ currentRecord.userId }}</el-descriptions-item> -->
          <el-descriptions-item label="用户名">{{ currentRecord.username }}</el-descriptions-item>
          <el-descriptions-item label="注册邮箱">{{ currentRecord.registerEmail }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-card class="detail-card">
        <div slot="header">
          <span class="card-title">提现信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="提现金额">
            <span class="amount-text">{{ formatNumber(currentRecord.amount) }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="手续费">
            <span class="fee-text">{{ formatNumber(currentRecord.fee) }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="实际到账">
            <span class="real-amount-text">{{ formatNumber(currentRecord.realAmount) }}USDT</span>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="提现地址" :span="2">
            <el-tooltip :content="currentRecord.address" placement="top">
              <span>{{ currentRecord.address || '-' }}</span>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item label="链名称">{{ currentRecord.chainName || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
       

      <el-card class="detail-card">
        <div slot="header">
          <span class="card-title">其他信息</span>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请时间">{{ formatDateTime(currentRecord.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(currentRecord.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentRecord.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="提现审核" :visible.sync="auditVisible" width="500px">
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="remark">
          <el-input 
            type="textarea" 
            v-model="auditForm.remark"
            :rows="3"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="auditVisible = false">取 消</el-button>
        <el-button 
          type="primary" 
          @click="submitAudit"
          :loading="auditLoading"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWithdrawList, getWithdrawStatistics, auditWithdraw, exportWithdrawRecord } from '@/api/finance/withdraw'
import { getParams } from '@/api/system/params'

export default {
  name: 'FinanceWithdrawRecord',
  data() {
    return {
      loading: false,
      // 查询参数
      listQuery: {
        page: 1,
        limit: 10,
        username: '',
        status: '',
        startDate: '',
        endDate: ''
      },
      dateRange: [],
      total: 0,
      tableData: [],
      // 详情相关
      detailVisible: false,
      currentRecord: {},
      // 审核相关
      auditVisible: false,
      auditForm: {
        status: 1,
        remark: ''
      },
      // ,
      //   remark: [
      //     { required: true, message: '请输入审核意见', trigger: 'blur' }
      //   ]
      auditRules: {
        status: [
          { required: true, message: '请选择审核结果', trigger: 'change' }
        ]
      },
      // 汇总数据
      summary: {
        todayAmount: 0,
        todayCount: 0,
        pendingAmount: 0,
        pendingCount: 0,
        totalPassAmount: 0
      },
      exportLoading: false,
      cooldownTimers: {}, // 新增：用于存储倒计时定时器
      auditLoading: false, // 添加审核提交的 loading 状态
      // 系统参数
      systemParams: {
        autoWithdraw: 0, // 是否自动提现，1为开启
        maxAutoWithdraw: 0 // 最大自动处理金额
      }
    }
  },
  created() {
    this.getList()
    this.getStatistics()
    this.getSystemParams()
  },
  methods: {
    formatNumber(num) {
      if (!num && num !== 0) return '0.00'
      return parseFloat(num).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    formatDateTime(datetime) {
      if (!datetime) return '-'
      const date = new Date(datetime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    getStatusType(status) {
      const map = {
        0: 'info',    // 待审核
        1: 'success', // 已通过
        2: 'danger',  // 已拒绝
        3: 'warning'  // 未通过
      }
      return map[status] || 'info'
    },
    getStatusText(status) {
      const map = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝',
        3: '未通过'
      }
      return map[status] || '未知'
    },
    async getList() {
      this.loading = true
      try {
        const res = await getWithdrawList(this.listQuery)
        if (res.code === 0) {
          this.tableData = res.records || []
          this.total = res.total || 0
          
          // 清除所有现有的定时器
          Object.values(this.cooldownTimers).forEach(timer => clearInterval(timer))
          this.cooldownTimers = {}
          
          // 为需要倒计时的记录设置定时器
          this.tableData.forEach(record => {
            if (record.status === 0 && this.isWithinCooldown(record.createTime, record.status)) {
              this.cooldownTimers[record.id] = setInterval(() => {
                if (!this.isWithinCooldown(record.createTime, record.status)) {
                  clearInterval(this.cooldownTimers[record.id])
                  delete this.cooldownTimers[record.id]
                }
                this.$forceUpdate()
              }, 1000)
            }
          })
        } else {
          this.$message.error(res.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取提现记录失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },
    async getStatistics() {
      try {
        const res = await getWithdrawStatistics()
        if (res.code === 0) {
          this.summary = res.data || {
            todayAmount: 0,
            todayCount: 0,
            pendingAmount: 0,
            pendingCount: 0,
            totalPassAmount: 0
          }
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },
    async getSystemParams() {
      try {
        const res = await getParams()
        if (res.code === 0) {
          this.systemParams = {
            autoWithdraw: res.data.autoWithdraw || 0,
            maxAutoWithdraw: res.data.maxAutoWithdraw || 0
          }
        }
      } catch (error) {
        console.error('获取系统参数失败:', error)
      }
    },
    handleSearch() {
      this.listQuery.page = 1
      this.getList()
    },
    handleReset() {
      this.dateRange = []
      this.listQuery = {
        page: 1,
        limit: 10,
        username: '',
        status: '',
        startDate: '',
        endDate: ''
      }
      this.getList()
    },
    handleDateRangeChange(val) {
      if (val) {
        this.listQuery.startDate = val[0]
        this.listQuery.endDate = val[1]
      } else {
        this.listQuery.startDate = ''
        this.listQuery.endDate = ''
      }
      this.handleSearch()
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },
    handleDetail(row) {
      this.currentRecord = { ...row }
      this.detailVisible = true
    },
    handleAudit(row) {
      this.currentRecord = row
      this.auditForm = {
        status: 1,
        remark: ''
      }
      this.auditVisible = true
    },
    async submitAudit() {
      this.$refs.auditForm.validate(async valid => {
        if (valid) {
          try {
            await this.$confirm(
              `确认${this.auditForm.status === 1 ? '通过' : '拒绝'}该提现申请吗？`, 
              '提示', 
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
            
            this.auditLoading = true // 开启 loading
            
            const res = await auditWithdraw(this.currentRecord.id, {
              status: this.auditForm.status,
              remark: this.auditForm.remark
            })

            if (res.code === 0) {
              this.$message.success(res.msg || '审核成功')
              this.auditVisible = false
              this.getList()
              this.getStatistics()
            } else {
              this.$message.error(res.msg || '审核失败,请查看服务费余额或者账户余额是否足够！')
            }
          } catch (error) {
            if (error === 'cancel') return
            console.error('审核失败:', error)
            this.$message.error(error.message || '审核失败')
          } finally {
            this.auditLoading = false // 关闭 loading
          }
        }
      })
    },
    async handleExport() {
      try {
        this.exportLoading = true
        
        // 构建导出参数
        const params = {
          username: this.listQuery.username,
          status: this.listQuery.status,
          startDate: this.listQuery.startDate,
          endDate: this.listQuery.endDate
        }

        const res = await exportWithdrawRecord(params)
        
        // 创建blob对象
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
        
        // 创建下载链接
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = '提现记录.xlsx'
        
        // 触发点击下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(link.href)

        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      } finally {
        this.exportLoading = false
      }
    },
    // 新增方法：检查是否在冷却时间内
    isWithinCooldown(createTime, status) {
      if (!createTime) return false
      const cooldownEndTime = new Date(createTime).getTime() + 3 * 60 * 1000 // 3分钟
      return Date.now() < cooldownEndTime && status === 0
    },
    // 新增方法：获取审核按钮文本
    getAuditButtonText(createTime, status) {
      if (!this.isWithinCooldown(createTime, status)) {
        return '审核'
      }
      const cooldownEndTime = new Date(createTime).getTime() + 3 * 60 * 1000
      const remainingTime = Math.ceil((cooldownEndTime - Date.now()) / 1000)
      return `审核 (${remainingTime}s)`
    },
    // 判断是否应该显示自动处理
    shouldShowAutoProcess(row) {
      // 只有待审核状态才考虑自动处理
      if (row.status !== 0) return false

      // 检查是否开启自动提现
      if (this.systemParams.autoWithdraw !== 1) return false

      // 检查提现金额是否小于等于最大自动处理金额
      return parseFloat(row.amount) <= parseFloat(this.systemParams.maxAutoWithdraw)
    },
    // 在组件销毁时清除所有定时器
    beforeDestroy() {
      Object.values(this.cooldownTimers).forEach(timer => clearInterval(timer))
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .summary-container {
    margin-bottom: 20px;
    
    .summary-item {
      background: #232323 !important;
      border: 1.5px solid #FFD700 !important;
      border-radius: 10px !important;
      box-shadow: 0 2px 12px 0 #000a !important;
      padding: 20px 20px 20px 28px !important;
      text-align: left !important;

      .label {
        color: #FFD700 !important;
        font-size: 14px;
        font-weight: bold !important;
        margin-bottom: 10px;
      }

      .value {
        color: #fff !important;
        font-size: 26px !important;
        font-weight: bold;
      }
    }
  }

  .filter-container {
    padding-bottom: 20px;
    .filter-item {
      margin-right: 10px;
    }
  }

  .pagination-container {
    padding: 20px 0;
    text-align: right;
  }
}

.detail-card {
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }

  .card-title {
    font-size: 15px;
    font-weight: bold;
    color: #303133;
  }

  ::v-deep .el-card__header {
    padding: 10px 15px;
    border-bottom: 1px solid #EBEEF5;
    background-color: #F5F7FA;
  }

  ::v-deep .el-card__body {
    padding: 15px;
  }

  .amount-text {
    color: #f56c6c;
    font-weight: bold;
  }

  .fee-text {
    color: #909399;
  }

  .real-amount-text {
    color: #67C23A;
    font-weight: bold;
  }
}

::v-deep .el-dialog__body {
  padding: 20px;
}

// 自动处理按钮样式
::v-deep .auto-process-btn {
  color: #67C23A !important;

  &:hover {
    color: #5daf34 !important;
  }
}

// 更强的样式覆盖
::v-deep .el-button.el-button--text.auto-process-btn {
  color: #67C23A !important;

  &:hover {
    color: #5daf34 !important;
  }
}
</style> 