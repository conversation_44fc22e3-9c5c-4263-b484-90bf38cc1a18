{"ast": null, "code": "import _regeneratorRuntime from \"E:/\\u6700\\u65B0\\u9879\\u76EE\\u6587\\u4EF6/\\u4EA4\\u6613\\u6240/adminweb/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _objectSpread from \"E:/\\u6700\\u65B0\\u9879\\u76EE\\u6587\\u4EF6/\\u4EA4\\u6613\\u6240/adminweb/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"E:/\\u6700\\u65B0\\u9879\\u76EE\\u6587\\u4EF6/\\u4EA4\\u6613\\u6240/adminweb/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getParams, updateParams } from '@/api/system/params';\nexport default {\n  name: 'SystemParams',\n  data: function data() {\n    return {\n      form: {\n        id: undefined,\n        minTransfer: 100.00,\n        maxTransfer: 50000.00,\n        transferFee: 0.00,\n        enableTransfer: 1,\n        minWithdraw: 100.00,\n        maxWithdraw: 50000.00,\n        maxAutoWithdraw: 200.00,\n        withdrawFee: 1.00,\n        enableWithdraw: 1,\n        autoWithdraw: 1,\n        enableInternalTransfer: 1,\n        minCopyTrade: 900.00,\n        tradeProfitRate: 50.00,\n        copyTradeAccountRate: 5.00,\n        copyTradeFee: 20.00,\n        platformFeeRate: 40.00\n      }\n    };\n  },\n  created: function created() {\n    this.getParamsData();\n  },\n  methods: {\n    // 获取参数数据\n    getParamsData: function getParamsData() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.prev = 0;\n              _context.next = 3;\n              return getParams();\n            case 3:\n              res = _context.sent;\n              if (res.code === 0) {\n                _this.form = _objectSpread(_objectSpread({}, _this.form), res.data);\n              } else {\n                _this.$message.error(res.msg || '获取参数失败');\n              }\n              _context.next = 11;\n              break;\n            case 7:\n              _context.prev = 7;\n              _context.t0 = _context[\"catch\"](0);\n              console.error('获取参数失败:', _context.t0);\n              _this.$message.error('获取参数失败');\n            case 11:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, null, [[0, 7]]);\n      }))();\n    },\n    // 提交表单\n    handleSubmit: function handleSubmit() {\n      var _this2 = this;\n      this.$refs.form.validate(function (valid) {\n        if (valid) {\n          _this2.$confirm('确认要保存修改吗？', '提示', {\n            type: 'warning'\n          }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n            var res;\n            return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n              while (1) switch (_context2.prev = _context2.next) {\n                case 0:\n                  _context2.prev = 0;\n                  _context2.next = 3;\n                  return updateParams(_this2.form);\n                case 3:\n                  res = _context2.sent;\n                  if (res.code === 0) {\n                    _this2.$message.success('保存成功');\n                  } else {\n                    _this2.$message.error(res.msg || '保存失败');\n                  }\n                  _context2.next = 11;\n                  break;\n                case 7:\n                  _context2.prev = 7;\n                  _context2.t0 = _context2[\"catch\"](0);\n                  console.error('保存失败:', _context2.t0);\n                  _this2.$message.error('保存失败');\n                case 11:\n                case \"end\":\n                  return _context2.stop();\n              }\n            }, _callee2, null, [[0, 7]]);\n          })))[\"catch\"](function () {});\n        }\n      });\n    },\n    // 重置表单\n    resetForm: function resetForm() {\n      this.$refs.form.resetFields();\n      this.getParamsData();\n    }\n  }\n};", "map": {"version": 3, "names": ["getParams", "updateParams", "name", "data", "form", "id", "undefined", "minTransfer", "maxTransfer", "transferFee", "enableTransfer", "minWithdraw", "max<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON>Withdraw", "withdrawFee", "enableWithdraw", "autoWithdraw", "enableInternalTransfer", "minCopyTrade", "tradeProfitRate", "copyTradeAccountRate", "copyTradeFee", "platformFeeRate", "created", "getParamsData", "methods", "_this", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_callee$", "_context", "prev", "next", "sent", "code", "_objectSpread", "$message", "error", "msg", "t0", "console", "stop", "handleSubmit", "_this2", "$refs", "validate", "valid", "$confirm", "type", "then", "_callee2", "_callee2$", "_context2", "success", "resetForm", "resetFields"], "sources": ["src/views/system/params/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-card\">\r\n      <el-form :model=\"form\" ref=\"form\" label-width=\"150px\" class=\"params-form\">\r\n        <!-- 转账相关设置 -->\r\n        <div class=\"section-title\">转账设置</div>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"最低转账限额\">\r\n              <el-input-number \r\n                v-model=\"form.minTransfer\" \r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                :step=\"100\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">USDT</span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"最高转账限额\">\r\n              <el-input-number \r\n                v-model=\"form.maxTransfer\" \r\n                :min=\"form.minTransfer\"\r\n                :precision=\"2\"\r\n                :step=\"1000\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">USDT</span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"转账手续费\">\r\n              <el-input-number \r\n                v-model=\"form.transferFee\" \r\n                :min=\"0\"\r\n                :max=\"100\"\r\n                :precision=\"2\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <!-- <span class=\"unit\">%</span> -->\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"是否允许转账\">\r\n              <el-switch\r\n                v-model=\"form.enableTransfer\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <!-- <el-col :span=\"8\">\r\n            <el-form-item label=\"是否允许内部转账\">\r\n              <el-switch\r\n                v-model=\"form.enableInternalTransfer\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n              />\r\n            </el-form-item>\r\n          </el-col> -->\r\n        </el-row>\r\n\r\n        <!-- 提现相关设置 -->\r\n        <div class=\"section-title\">提现设置</div>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"最低提现限额\">\r\n              <el-input-number \r\n                v-model=\"form.minWithdraw\" \r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                :step=\"100\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">USDT</span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"最高提现限额\">\r\n              <el-input-number \r\n                v-model=\"form.maxWithdraw\" \r\n                :min=\"form.minWithdraw\"\r\n                :precision=\"2\"\r\n                :step=\"1000\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">USDT</span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"自动提现限额\">\r\n              <el-input-number \r\n                v-model=\"form.maxAutoWithdraw\" \r\n                :min=\"0\"\r\n                :max=\"form.maxWithdraw\"\r\n                :precision=\"2\"\r\n                :step=\"100\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">USDT</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"提现手续费\">\r\n              <el-input-number \r\n                v-model=\"form.withdrawFee\" \r\n                :min=\"0\"\r\n                :max=\"100\"\r\n                :precision=\"2\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <!-- <span class=\"unit\">%</span> -->\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"是否允许提现\">\r\n              <el-switch\r\n                v-model=\"form.enableWithdraw\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"是否自动提现\">\r\n              <el-switch\r\n                v-model=\"form.autoWithdraw\"\r\n                :active-value=\"1\"\r\n                :inactive-value=\"0\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 跟单设置 -->\r\n        <div class=\"section-title\">跟单设置</div>\r\n        <el-row :gutter=\"20\">\r\n          <!-- <el-col :span=\"8\">\r\n            <el-form-item label=\"最低跟单额度\">\r\n              <el-input-number \r\n                v-model=\"form.minCopyTrade\" \r\n                :min=\"0\"\r\n                :precision=\"2\"\r\n                :step=\"100\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">元</span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"交易盈利比例\">\r\n              <el-input-number \r\n                v-model=\"form.tradeProfitRate\" \r\n                :min=\"0\"\r\n                :max=\"100\"\r\n                :precision=\"2\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">%</span>\r\n            </el-form-item>\r\n          </el-col> -->\r\n      \r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n              <!-- <el-col :span=\"8\">\r\n            <el-form-item label=\"跟单账户比例\">\r\n              <el-input-number \r\n                v-model=\"form.copyTradeAccountRate\" \r\n                :min=\"0\"\r\n                :max=\"100\"\r\n                :precision=\"2\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">%</span>\r\n            </el-form-item>\r\n          </el-col> -->\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"跟单手续费比例\">\r\n              <el-input-number \r\n                v-model=\"form.copyTradeFee\" \r\n                :min=\"0\"\r\n                :max=\"100\"\r\n                :precision=\"2\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">%</span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"平台手续费比例\">\r\n              <el-input-number \r\n                v-model=\"form.platformFeeRate\" \r\n                :min=\"0\"\r\n                :max=\"100\"\r\n                :precision=\"2\"\r\n                style=\"width: 200px\"\r\n              />\r\n              <span class=\"unit\">%</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSubmit\">保存设置</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getParams, updateParams } from '@/api/system/params'\r\n\r\nexport default {\r\n  name: 'SystemParams',\r\n  data() {\r\n    return {\r\n      form: {\r\n        id: undefined,\r\n        minTransfer: 100.00,\r\n        maxTransfer: 50000.00,\r\n        transferFee: 0.00,\r\n        enableTransfer: 1,\r\n        minWithdraw: 100.00,\r\n        maxWithdraw: 50000.00,\r\n        maxAutoWithdraw: 200.00,\r\n        withdrawFee: 1.00,\r\n        enableWithdraw: 1,\r\n        autoWithdraw: 1,\r\n        enableInternalTransfer: 1,\r\n        minCopyTrade: 900.00,\r\n        tradeProfitRate: 50.00,\r\n        copyTradeAccountRate: 5.00,\r\n        copyTradeFee: 20.00,\r\n        platformFeeRate: 40.00\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getParamsData()\r\n  },\r\n  methods: {\r\n    // 获取参数数据\r\n    async getParamsData() {\r\n      try {\r\n        const res = await getParams()\r\n        if (res.code === 0) {\r\n          this.form = {\r\n            ...this.form,\r\n            ...res.data\r\n          }\r\n        } else {\r\n          this.$message.error(res.msg || '获取参数失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取参数失败:', error)\r\n        this.$message.error('获取参数失败')\r\n      }\r\n    },\r\n\r\n    // 提交表单\r\n    handleSubmit() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.$confirm('确认要保存修改吗？', '提示', {\r\n            type: 'warning'\r\n          }).then(async () => {\r\n            try {\r\n              const res = await updateParams(this.form)\r\n              if (res.code === 0) {\r\n                this.$message.success('保存成功')\r\n              } else {\r\n                this.$message.error(res.msg || '保存失败')\r\n              }\r\n            } catch (error) {\r\n              console.error('保存失败:', error)\r\n              this.$message.error('保存失败')\r\n            }\r\n          }).catch(() => {})\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$refs.form.resetFields()\r\n      this.getParamsData()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  padding: 20px;\r\n\r\n  .params-form {\r\n    padding: 20px;\r\n\r\n    .section-title {\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n      margin: 30px 0 20px;\r\n      padding-left: 10px;\r\n      border-left: 4px solid #409EFF;\r\n      \r\n      &:first-child {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n\r\n    .unit {\r\n      margin-left: 10px;\r\n      color: #909399;\r\n    }\r\n\r\n    .el-form-item {\r\n      margin-bottom: 22px;\r\n    }\r\n\r\n    .el-input-number {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style> "], "mappings": ";;;AAyNA,SAAAA,SAAA,EAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,EAAA,EAAAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,WAAA;QACAC,cAAA;QACAC,WAAA;QACAC,WAAA;QACAC,eAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,eAAA;QACAC,oBAAA;QACAC,YAAA;QACAC,eAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MAAA,OAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEApC,SAAA;YAAA;cAAA+B,GAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAZ,KAAA,CAAAtB,IAAA,GAAAmC,aAAA,CAAAA,aAAA,KACAb,KAAA,CAAAtB,IAAA,GACA2B,GAAA,CAAA5B,IAAA,CACA;cACA;gBACAuB,KAAA,CAAAc,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;cACA;cAAAR,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAEAU,OAAA,CAAAH,KAAA,YAAAP,QAAA,CAAAS,EAAA;cACAjB,KAAA,CAAAc,QAAA,CAAAC,KAAA;YAAA;YAAA;cAAA,OAAAP,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAf,OAAA;MAAA;IAEA;IAEA;IACAgB,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA5C,IAAA,CAAA6C,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAI,QAAA;YACAC,IAAA;UACA,GAAAC,IAAA,cAAA1B,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,UAAAyB,SAAA;YAAA,IAAAvB,GAAA;YAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAuB,UAAAC,SAAA;cAAA,kBAAAA,SAAA,CAAArB,IAAA,GAAAqB,SAAA,CAAApB,IAAA;gBAAA;kBAAAoB,SAAA,CAAArB,IAAA;kBAAAqB,SAAA,CAAApB,IAAA;kBAAA,OAEAnC,YAAA,CAAA8C,MAAA,CAAA3C,IAAA;gBAAA;kBAAA2B,GAAA,GAAAyB,SAAA,CAAAnB,IAAA;kBACA,IAAAN,GAAA,CAAAO,IAAA;oBACAS,MAAA,CAAAP,QAAA,CAAAiB,OAAA;kBACA;oBACAV,MAAA,CAAAP,QAAA,CAAAC,KAAA,CAAAV,GAAA,CAAAW,GAAA;kBACA;kBAAAc,SAAA,CAAApB,IAAA;kBAAA;gBAAA;kBAAAoB,SAAA,CAAArB,IAAA;kBAAAqB,SAAA,CAAAb,EAAA,GAAAa,SAAA;kBAEAZ,OAAA,CAAAH,KAAA,UAAAe,SAAA,CAAAb,EAAA;kBACAI,MAAA,CAAAP,QAAA,CAAAC,KAAA;gBAAA;gBAAA;kBAAA,OAAAe,SAAA,CAAAX,IAAA;cAAA;YAAA,GAAAS,QAAA;UAAA,CAEA;QACA;MACA;IACA;IAEA;IACAI,SAAA,WAAAA,UAAA;MACA,KAAAV,KAAA,CAAA5C,IAAA,CAAAuD,WAAA;MACA,KAAAnC,aAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}